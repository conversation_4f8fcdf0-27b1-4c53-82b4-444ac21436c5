import 'package:flutter/material.dart';

class MessagesScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Container(
            color: Colors.white,
            child: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.only(bottom: 12, top: 12, left: 16, right: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    bottom: BorderSide(
                      color: const Color(0xFFE2E8F0),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Text(
                      'Poruke',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF0A0A0A),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: 4,
        itemBuilder: (context, index) {
          return Card(
            margin: EdgeInsets.only(bottom: 12),
            child: ListTile(
              contentPadding: EdgeInsets.all(16),
              leading: CircleAvatar(
                backgroundColor: Colors.green,
                child: Icon(
                  Icons.business,
                  color: Colors.white,
                ),
              ),
              title: Text(
                'Kompanija ${index + 1}',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 4),
                  Text('Poslednja poruka...'),
                  SizedBox(height: 4),
                  Text(
                    '${DateTime.now().subtract(Duration(hours: index + 1)).hour}:${DateTime.now().minute.toString().padLeft(2, '0')}',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
              trailing: index == 0
                  ? Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    )
                  : Icon(Icons.arrow_forward_ios),
              onTap: () {
                // Open chat with company
              },
            ),
          );
        },
      ),
          ),
        ],
      ),
    );
  }
}