import 'package:flutter/material.dart';

class ApplicationsScreen extends StatefulWidget {
  @override
  _ApplicationsScreenState createState() => _ApplicationsScreenState();
}

class _ApplicationsScreenState extends State<ApplicationsScreen> {
  String _activeTab = 'accepted';

  final List<Map<String, dynamic>> _allApplications = [
    {
      'id': '1',
      'jobTitle': 'Senior Software Engineer',
      'company': 'TechCorp GmbH',
      'applicationDate': '15. decembar 2024',
      'status': 'accepted',
      'logo': '🏢'
    },
    {
      'id': '2',
      'jobTitle': 'Frontend Developer',
      'company': 'WebSolutions',
      'applicationDate': '12. decembar 2024',
      'status': 'pending',
      'logo': '🏢'
    },
    {
      'id': '3',
      'jobTitle': 'Marketing Manager',
      'company': 'Alpine Solutions',
      'applicationDate': '10. decembar 2024',
      'status': 'accepted',
      'logo': '🏢'
    },
    {
      'id': '4',
      'jobTitle': 'Data Analyst',
      'company': 'DataFlow AG',
      'applicationDate': '8. decembar 2024',
      'status': 'rejected',
      'logo': '🏢'
    },
    {
      'id': '5',
      'jobTitle': 'UX Designer',
      'company': 'DesignStudio',
      'applicationDate': '5. decembar 2024',
      'status': 'pending',
      'logo': '🏢'
    },
    {
      'id': '6',
      'jobTitle': 'Backend Developer',
      'company': 'ServerTech',
      'applicationDate': '3. decembar 2024',
      'status': 'rejected',
      'logo': '🏢'
    },
  ];

  final List<Map<String, dynamic>> _tabConfig = [
    {
      'id': 'accepted',
      'label': 'Prihvaćeno',
      'icon': Icons.check_circle,
      'color': Colors.green.shade600,
      'bgColor': Colors.green.shade50,
      'borderColor': Colors.green.shade200
    },
    {
      'id': 'pending',
      'label': 'Na pregledu',
      'icon': Icons.schedule,
      'color': Colors.orange.shade600,
      'bgColor': Colors.orange.shade50,
      'borderColor': Colors.orange.shade200
    },
    {
      'id': 'rejected',
      'label': 'Odbijeno',
      'icon': Icons.close,
      'color': Colors.red.shade600,
      'bgColor': Colors.red.shade50,
      'borderColor': Colors.red.shade200
    },
  ];

  List<Map<String, dynamic>> _getApplicationsByStatus(String status) {
    return _allApplications.where((app) => app['status'] == status).toList();
  }

  Map<String, dynamic>? _getStatusConfig(String status) {
    return _tabConfig.firstWhere((tab) => tab['id'] == status, orElse: () => _tabConfig[0]);
  }

  @override
  Widget build(BuildContext context) {
    final filteredApplications = _getApplicationsByStatus(_activeTab);
    
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Column(
        children: [
          // Header
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
            ),
            child: SafeArea(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.shade200),
                  ),
                ),
                child: Row(
                  children: [
                    Text(
                      'Prijave',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Tab Navigation
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: Row(
              children: _tabConfig.map((tab) {
                final isActive = _activeTab == tab['id'];
                return Expanded(
                  child: GestureDetector(
                    onTap: () => setState(() => _activeTab = tab['id']),
                    child: Container(
                      margin: EdgeInsets.symmetric(horizontal: 4),
                      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                      decoration: BoxDecoration(
                        color: isActive ? tab['bgColor'] : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isActive ? tab['borderColor'] : Colors.transparent,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            tab['icon'],
                            size: 16,
                            color: isActive ? tab['color'] : Colors.grey.shade600,
                          ),
                          SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              tab['label'],
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: isActive ? tab['color'] : Colors.grey.shade600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          
          // Main Content
          Expanded(
            child: filteredApplications.isNotEmpty
                ? _buildApplicationList(filteredApplications)
                : _buildEmptyState(),
          ),
        ],
      ),
    );
  }

  Widget _buildApplicationList(List<Map<String, dynamic>> applications) {
    return ListView.builder(
      padding: EdgeInsets.fromLTRB(16, 24, 16, 80),
      itemCount: applications.length,
      itemBuilder: (context, index) {
        final application = applications[index];
        final statusConfig = _getStatusConfig(application['status'])!;
        
        return Container(
          margin: EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(16),
            child: InkWell(
              borderRadius: BorderRadius.circular(16),
              onTap: () {
                print('Application clicked: ${application['id']}');
              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: Border(
                    left: BorderSide(
                      color: statusConfig['color'],
                      width: 4,
                    ),
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header row
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  application['jobTitle'],
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                    height: 1.2,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: 8),
                                Row(
                                  children: [
                                    Icon(
                                      Icons.business,
                                      size: 16,
                                      color: Colors.grey.shade600,
                                    ),
                                    SizedBox(width: 8),
                                    Flexible(
                                      child: Text(
                                        application['company'],
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey.shade600,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 12),
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                application['logo'],
                                style: TextStyle(fontSize: 20),
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      SizedBox(height: 16),
                      
                      // Footer row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 16,
                                color: Colors.grey.shade600,
                              ),
                              SizedBox(width: 6),
                              Text(
                                application['applicationDate'],
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: statusConfig['bgColor'],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: statusConfig['borderColor']),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  statusConfig['icon'],
                                  size: 16,
                                  color: statusConfig['color'],
                                ),
                                SizedBox(width: 6),
                                Text(
                                  statusConfig['label'],
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: statusConfig['color'],
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    final activeConfig = _tabConfig.firstWhere((tab) => tab['id'] == _activeTab);
    
    String title, description;
    switch (_activeTab) {
      case 'accepted':
        title = 'Nema prihvaćenih prijava';
        description = 'Kada se vaše prijave prihvate, pojaviće se ovdje.';
        break;
      case 'pending':
        title = 'Nema prijava na pregledu';
        description = 'Prijave koje su trenutno na pregledu će se prikazati ovdje.';
        break;
      case 'rejected':
        title = 'Nema odbijenih prijava';
        description = 'Odbijene prijave će biti prikazane u ovoj sekciji.';
        break;
      default:
        title = 'Nema prijava';
        description = 'Vaše prijave će se prikazati ovdje.';
    }
    
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                shape: BoxShape.circle,
              ),
              child: Icon(
                activeConfig['icon'],
                size: 32,
                color: Colors.grey.shade400,
              ),
            ),
            SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

}