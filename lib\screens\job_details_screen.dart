import 'package:flutter/material.dart';

class JobDetailsScreen extends StatefulWidget {
  @override
  _JobDetailsScreenState createState() => _JobDetailsScreenState();
}

class _JobDetailsScreenState extends State<JobDetailsScreen> {
  final Map<String, dynamic> job = {
    'id': '4',
    'title': 'Frontend Developer',
    'company': 'WebSolutions',
    'country': 'Njemačka',
    'city': 'Hamburg',
    'salary': '€3,000 - €4,200',
    'type': 'Puno radno vrijeme',
    'logo': '🏢',
    'countryFlag': '🇩🇪',
    'description': 'Pridružite se našem dinamičnom timu kao Frontend Developer i radite na inovativnim web projektima. Tražimo kreativnu osobu koja će pomoći u razvoju modernih korisničkih interfejsa koristeći najnovije tehnologije. Ova pozicija nudi odličnu priliku za profesionalni rast u međunarodnom okruženju.',
    'requirements': [
      'Njemački jezik - nivo B2 ili viši',
      'Vozačka dozvola kategorije B',
      'Minimum 2 godine iskustva u React.js',
      'Poznavanje TypeScript-a',
      'Iskustvo sa Tailwind CSS',
      'Razumijevanje Git workflow-a',
      'Komunikacijske vještine'
    ],
    'benefits': [
      'Fleksibilno radno vrijeme',
      'Mogućnost rada od kuće',
      'Privatno zdravstveno osiguranje',
      'Godišnji bonus',
      'Besplatna kafa i grickalice',
      'Tim building eventi',
      'Mogućnost napredovanja',
      'Moderni radni prostor'
    ]
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.only(bottom: 100),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildJobHeaderCard(),
                  _buildJobDescription(),
                  _buildRequirementsSection(),
                  _buildBenefitsSection(),
                  _buildApplyButton(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      color: Colors.white,
      child: SafeArea(
        child: Container(
          padding: EdgeInsets.fromLTRB(16, 12, 16, 12),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: const Color(0xFF3B82F6).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.arrow_back,
                    color: const Color(0xFF1E3A8A),
                    size: 20,
                  ),
                ),
              ),
              SizedBox(width: 12),
              Text(
                'Detalji posla',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF0A0A0A),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildJobHeaderCard() {
    return Container(
      margin: EdgeInsets.fromLTRB(16, 16, 16, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border(
          left: BorderSide(
            color: const Color(0xFF1E40AF),
            width: 4,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF3B82F6).withOpacity(0.05),
          borderRadius: BorderRadius.circular(16),
        ),
        padding: EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        job['title'],
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF0A0A0A),
                          height: 1.2,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        job['company'],
                        style: TextStyle(
                          color: const Color(0xFF64748B),
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                      SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            job['countryFlag'],
                            style: TextStyle(fontSize: 18),
                          ),
                          SizedBox(width: 8),
                          Text(
                            '${job['city']}, ${job['country']}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF0A0A0A),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  job['logo'],
                  style: TextStyle(fontSize: 48),
                ),
              ],
            ),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.euro_outlined,
                      size: 20,
                      color: const Color(0xFF1E3A8A),
                    ),
                    SizedBox(width: 4),
                    Text(
                      job['salary'],
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF1E3A8A),
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF1F5F9),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    job['type'],
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF475569),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobDescription() {
    return Container(
      margin: EdgeInsets.fromLTRB(16, 24, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Opis posla',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF0A0A0A),
            ),
          ),
          SizedBox(height: 12),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            padding: EdgeInsets.all(20),
            child: Text(
              job['description'],
              style: TextStyle(
                fontSize: 14,
                color: const Color(0xFF0A0A0A),
                height: 1.6,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirementsSection() {
    return Container(
      margin: EdgeInsets.fromLTRB(16, 24, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Zahtjevi',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF0A0A0A),
            ),
          ),
          SizedBox(height: 12),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            padding: EdgeInsets.all(20),
            child: Column(
              children: job['requirements'].asMap().entries.map<Widget>((entry) {
                int index = entry.key;
                String requirement = entry.value;
                IconData icon = _getRequirementIcon(index);
                
                return Padding(
                  padding: EdgeInsets.only(bottom: index < job['requirements'].length - 1 ? 12 : 0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 2),
                        child: Icon(
                          icon,
                          size: 16,
                          color: const Color(0xFF1E3A8A),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          requirement,
                          style: TextStyle(
                            fontSize: 14,
                            color: const Color(0xFF0A0A0A),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsSection() {
    return Container(
      margin: EdgeInsets.fromLTRB(16, 24, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Benefiti',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF0A0A0A),
            ),
          ),
          SizedBox(height: 12),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            padding: EdgeInsets.all(20),
            child: Column(
              children: job['benefits'].asMap().entries.map<Widget>((entry) {
                int index = entry.key;
                String benefit = entry.value;
                IconData icon = _getBenefitIcon(index);
                
                return Padding(
                  padding: EdgeInsets.only(bottom: index < job['benefits'].length - 1 ? 12 : 0),
                  child: Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 2),
                        child: Icon(
                          icon,
                          size: 16,
                          color: const Color(0xFF059669),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          benefit,
                          style: TextStyle(
                            fontSize: 14,
                            color: const Color(0xFF0A0A0A),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApplyButton() {
    return Container(
      margin: EdgeInsets.fromLTRB(16, 32, 16, 24),
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: ElevatedButton(
          onPressed: () {
            _showApplicationDialog();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF1E3A8A),
            foregroundColor: Colors.white,
            elevation: 4,
            shadowColor: const Color(0xFF1E3A8A).withOpacity(0.3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: Text(
            'Prijavite se za posao',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  IconData _getRequirementIcon(int index) {
    switch (index) {
      case 0:
        return Icons.language;
      case 1:
        return Icons.directions_car;
      default:
        return Icons.check_circle_outline;
    }
  }

  IconData _getBenefitIcon(int index) {
    switch (index) {
      case 0:
        return Icons.access_time;
      case 1:
        return Icons.business;
      case 2:
        return Icons.security;
      case 3:
        return Icons.euro;
      case 4:
        return Icons.coffee;
      case 5:
        return Icons.people;
      case 6:
        return Icons.trending_up;
      case 7:
        return Icons.favorite;
      default:
        return Icons.check_circle_outline;
    }
  }

  void _showApplicationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Prijava za posao'),
        content: Text('Da li ste sigurni da se želite prijaviti na poziciju "${job['title']}" u kompaniji ${job['company']}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Odustani'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Uspješno ste se prijavili na posao!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: Text('Prijavi se'),
          ),
        ],
      ),
    );
  }
}