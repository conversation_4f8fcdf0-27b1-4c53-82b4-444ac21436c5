import 'package:flutter/material.dart';
import '../widgets/job_card.dart';
import '../widgets/search_bar_with_filter.dart';
import 'job_details_screen.dart';

class JobsScreen extends StatefulWidget {
  @override
  _JobsScreenState createState() => _JobsScreenState();
}

class _JobsScreenState extends State<JobsScreen> {
  String _searchQuery = '';
  
  final List<Job> _featuredJobs = [
    Job(
      id: '1',
      title: 'Senior Software Engineer',
      company: 'TechCorp GmbH',
      country: 'Njemačka',
      city: 'Berlin',
      salary: '€4,500 - €6,000',
      logo: '🏢',
      countryFlag: '🇩🇪',
    ),
    Job(
      id: '2',
      title: 'Marketing Manager',
      company: 'Alpine Solutions',
      country: 'Austrija',
      city: 'Wien',
      salary: '€3,800 - €5,200',
      logo: '🏢',
      countryFlag: '🇦🇹',
    ),
    Job(
      id: '3',
      title: 'Data Analyst',
      company: 'DataFlow AG',
      country: 'Njemačka',
      city: 'München',
      salary: '€3,200 - €4,500',
      logo: '🏢',
      countryFlag: '🇩🇪',
    ),
  ];

  final List<Job> _regularJobs = [
    Job(
      id: '4',
      title: 'Frontend Developer',
      company: 'WebSolutions',
      country: 'Njemačka',
      city: 'Hamburg',
      salary: '€3,000 - €4,200',
      type: 'Puno radno vrijeme',
      logo: '🏢',
      countryFlag: '🇩🇪',
    ),
    Job(
      id: '5',
      title: 'Project Manager',
      company: 'BuildTech',
      country: 'Austrija',
      city: 'Salzburg',
      salary: '€4,000 - €5,500',
      type: 'Hibridno',
      logo: '🏢',
      countryFlag: '🇦🇹',
    ),
    Job(
      id: '6',
      title: 'UX Designer',
      company: 'DesignStudio',
      country: 'Njemačka',
      city: 'Frankfurt',
      salary: '€3,500 - €4,800',
      type: 'Remote',
      logo: '🏢',
      countryFlag: '🇩🇪',
    ),
    Job(
      id: '7',
      title: 'Backend Developer',
      company: 'ServerTech',
      country: 'Austrija',
      city: 'Graz',
      salary: '€3,800 - €5,000',
      type: 'Puno radno vrijeme',
      logo: '🏢',
      countryFlag: '🇦🇹',
    ),
  ];

  List<Job> get _filteredFeaturedJobs {
    if (_searchQuery.isEmpty) return _featuredJobs;
    
    return _featuredJobs.where((job) {
      final query = _searchQuery.toLowerCase();
      return job.title.toLowerCase().contains(query) ||
             job.company.toLowerCase().contains(query) ||
             job.city.toLowerCase().contains(query);
    }).toList();
  }

  List<Job> get _filteredRegularJobs {
    if (_searchQuery.isEmpty) return _regularJobs;
    
    return _regularJobs.where((job) {
      final query = _searchQuery.toLowerCase();
      return job.title.toLowerCase().contains(query) ||
             job.company.toLowerCase().contains(query) ||
             job.city.toLowerCase().contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Container(
            color: Colors.white,
            child: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    bottom: BorderSide(
                      color: const Color(0xFFE2E8F0),
                      width: 1,
                    ),
                  ),
                ),
                child: SearchBarWithFilter(
                  onSearch: (query) {
                    setState(() {
                      _searchQuery = query;
                    });
                  },
                ),
              ),
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.only(bottom: 80),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text(
                      'Izdvojeni poslovi',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF0A0A0A),
                      ),
                    ),
                  ),
                  Container(
                    height: 200,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _filteredFeaturedJobs.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: EdgeInsets.only(
                            right: index < _filteredFeaturedJobs.length - 1 ? 16 : 0,
                          ),
                          child: FeaturedJobCard(
                            job: _filteredFeaturedJobs[index],
                          ),
                        );
                      },
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text(
                      'Svi poslovi',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF0A0A0A),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: _filteredRegularJobs.map((job) {
                        return RegularJobCard(job: job);
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}