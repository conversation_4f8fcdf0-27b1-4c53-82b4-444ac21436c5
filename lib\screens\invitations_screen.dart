import 'package:flutter/material.dart';

class InvitationsScreen extends StatefulWidget {
  @override
  _InvitationsScreenState createState() => _InvitationsScreenState();
}

class _InvitationsScreenState extends State<InvitationsScreen> {
  String _activeTab = 'pending';

  final List<Map<String, dynamic>> _allInvitations = [
    {
      'id': '1',
      'jobTitle': 'Senior Flutter Developer',
      'company': 'TechCorp GmbH',
      'receivedDate': '18. decembar 2024',
      'status': 'pending',
      'logo': '🏢',
      'description': 'Kompanija vas poziva na razgovor za poziciju Senior Flutter Developer.',
    },
    {
      'id': '2',
      'jobTitle': 'UI/UX Designer',
      'company': 'DesignHub',
      'receivedDate': '16. decembar 2024',
      'status': 'pending',
      'logo': '🎨',
      'description': 'Zainteresirani smo za vaš profil i pozivamo vas na intervju.',
    },
    {
      'id': '3',
      'jobTitle': 'Project Manager',
      'company': 'Alpine Solutions',
      'receivedDate': '14. decembar 2024',
      'status': 'accepted',
      'logo': '🏢',
      'description': 'Prihvatili ste pozivnicu za poziciju Project Manager.',
    },
    {
      'id': '4',
      'jobTitle': 'Data Scientist',
      'company': 'DataFlow AG',
      'receivedDate': '12. decembar 2024',
      'status': 'pending',
      'logo': '📊',
      'description': 'Kompanija je zainteresirovana za vašu ekspertizu u oblasti data science.',
    },
    {
      'id': '5',
      'jobTitle': 'Marketing Specialist',
      'company': 'CreativeAgency',
      'receivedDate': '10. decembar 2024',
      'status': 'accepted',
      'logo': '📈',
      'description': 'Prihvatili ste pozivnicu za Marketing Specialist poziciju.',
    },
  ];

  final List<Map<String, dynamic>> _tabConfig = [
    {
      'id': 'pending',
      'label': 'Na čekanju',
      'icon': Icons.schedule,
      'color': Colors.orange.shade600,
      'bgColor': Colors.orange.shade50,
      'borderColor': Colors.orange.shade200
    },
    {
      'id': 'accepted',
      'label': 'Prihvaćeno',
      'icon': Icons.check_circle,
      'color': Colors.green.shade600,
      'bgColor': Colors.green.shade50,
      'borderColor': Colors.green.shade200
    },
  ];

  List<Map<String, dynamic>> _getInvitationsByStatus(String status) {
    return _allInvitations.where((invitation) => invitation['status'] == status).toList();
  }

  Map<String, dynamic>? _getStatusConfig(String status) {
    return _tabConfig.firstWhere((tab) => tab['id'] == status, orElse: () => _tabConfig[0]);
  }

  @override
  Widget build(BuildContext context) {
    final filteredInvitations = _getInvitationsByStatus(_activeTab);
    
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Column(
        children: [
          // Header
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
            ),
            child: SafeArea(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.shade200),
                  ),
                ),
                child: Row(
                  children: [
                    Text(
                      'Pozivnice',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Tab Navigation
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: Row(
              children: _tabConfig.map((tab) {
                final isActive = _activeTab == tab['id'];
                return Expanded(
                  child: GestureDetector(
                    onTap: () => setState(() => _activeTab = tab['id']),
                    child: Container(
                      margin: EdgeInsets.symmetric(horizontal: 4),
                      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                      decoration: BoxDecoration(
                        color: isActive ? tab['bgColor'] : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isActive ? tab['borderColor'] : Colors.transparent,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            tab['icon'],
                            size: 16,
                            color: isActive ? tab['color'] : Colors.grey.shade600,
                          ),
                          SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              tab['label'],
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: isActive ? tab['color'] : Colors.grey.shade600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          
          // Main Content
          Expanded(
            child: filteredInvitations.isNotEmpty
                ? _buildInvitationsList(filteredInvitations)
                : _buildEmptyState(),
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationsList(List<Map<String, dynamic>> invitations) {
    return ListView.builder(
      padding: EdgeInsets.fromLTRB(16, 24, 16, 80),
      itemCount: invitations.length,
      itemBuilder: (context, index) {
        final invitation = invitations[index];
        final statusConfig = _getStatusConfig(invitation['status'])!;
        
        return Container(
          margin: EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(16),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: Border(
                  left: BorderSide(
                    color: statusConfig['color'],
                    width: 4,
                  ),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header row
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                invitation['jobTitle'],
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                  height: 1.2,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: 8),
                              Row(
                                children: [
                                  Icon(
                                    Icons.business,
                                    size: 16,
                                    color: Colors.grey.shade600,
                                  ),
                                  SizedBox(width: 8),
                                  Flexible(
                                    child: Text(
                                      invitation['company'],
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey.shade600,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 12),
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Text(
                              invitation['logo'],
                              style: TextStyle(fontSize: 20),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    SizedBox(height: 12),
                    
                    // Description
                    Text(
                      invitation['description'],
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey.shade700,
                        height: 1.4,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    SizedBox(height: 16),
                    
                    // Footer row with date and buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Date in bottom left
                        Row(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              size: 16,
                              color: Colors.grey.shade600,
                            ),
                            SizedBox(width: 6),
                            Text(
                              invitation['receivedDate'],
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        
                        // Buttons in bottom right
                        if (invitation['status'] == 'pending') ...[
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Odbij button
                              Container(
                                height: 32,
                                child: OutlinedButton(
                                  onPressed: () => _declineInvitation(invitation['id']),
                                  style: OutlinedButton.styleFrom(
                                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                    side: BorderSide(color: Colors.red.shade300),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    minimumSize: Size(0, 32),
                                  ),
                                  child: Text(
                                    'Odbij',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.red.shade600,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 8),
                              // Pogledaj button
                              Container(
                                height: 32,
                                child: ElevatedButton(
                                  onPressed: () => _viewInvitation(invitation['id']),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue.shade600,
                                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    minimumSize: Size(0, 32),
                                    elevation: 0,
                                  ),
                                  child: Text(
                                    'Pogledaj',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ] else ...[
                          // Status badge for accepted invitations
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: statusConfig['bgColor'],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: statusConfig['borderColor']),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  statusConfig['icon'],
                                  size: 16,
                                  color: statusConfig['color'],
                                ),
                                SizedBox(width: 6),
                                Text(
                                  statusConfig['label'],
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: statusConfig['color'],
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    final activeConfig = _tabConfig.firstWhere((tab) => tab['id'] == _activeTab);
    
    String title, description;
    switch (_activeTab) {
      case 'pending':
        title = 'Nema pozivnica na čekanju';
        description = 'Pozivnice od kompanija će se prikazati ovdje kada ih primite.';
        break;
      case 'accepted':
        title = 'Nema prihvaćenih pozivnica';
        description = 'Pozivnice koje prihvatite će biti prikazane u ovoj sekciji.';
        break;
      default:
        title = 'Nema pozivnica';
        description = 'Vaše pozivnice će se prikazati ovdje.';
    }
    
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.mail_outline,
                size: 32,
                color: Colors.grey.shade400,
              ),
            ),
            SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  void _viewInvitation(String invitationId) {
    print('Pogledaj pozivnicu: $invitationId');
    // Navigate to job details or show invitation details
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Otvaranje pozivnice $invitationId...'),
        backgroundColor: Colors.blue.shade600,
      ),
    );
  }

  void _declineInvitation(String invitationId) {
    print('Odbij pozivnicu: $invitationId');
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Odbij pozivnicu'),
          content: Text('Da li ste sigurni da želite da odbijete ovu pozivnicu?'),
          actions: [
            TextButton(
              child: Text('Otkaži'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text('Odbij', style: TextStyle(color: Colors.red)),
              onPressed: () {
                Navigator.of(context).pop();
                // Remove invitation from pending and don't add to any other status
                setState(() {
                  _allInvitations.removeWhere((inv) => inv['id'] == invitationId);
                });
                
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Pozivnica je odbijena'),
                    backgroundColor: Colors.red.shade600,
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}