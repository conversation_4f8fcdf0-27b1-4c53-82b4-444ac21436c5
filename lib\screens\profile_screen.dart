import 'package:flutter/material.dart';

class ProfileScreen extends StatefulWidget {
  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  
  String _ime = '';
  String _prezime = '';
  String _telefon = '';
  String _email = '';
  String _drzava = 'Hrvatska';
  String _nemacki = 'Bez znanja';
  String _engleski = 'Be<PERSON> znanja';
  List<String> _vozacke = [];
  List<Map<String, String>> _obrazovanje = [];
  List<Map<String, String>> _iskustvo = [];

  final List<String> _jezici = ['Bez znanja', 'A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
  final List<String> _vozackeKategorije = ['A', 'B', 'C', 'D', 'E'];
  final List<String> _drzave = ['Hrvatska', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>st<PERSON><PERSON>', '<PERSON>lovenija', 'Bosna i Hercegovina', '<PERSON><PERSON><PERSON>'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Profil'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(Icons.save),
            onPressed: _saveProfile,
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSection('Osnovni podaci', [
                _buildTextField('Ime', (value) => _ime = value),
                _buildTextField('Prezime', (value) => _prezime = value),
                _buildTextField('Telefon', (value) => _telefon = value),
                _buildTextField('Email', (value) => _email = value, keyboardType: TextInputType.emailAddress),
                _buildDropdown('Država', _drzava, _drzave, (value) => setState(() => _drzava = value!)),
              ]),
              _buildSection('Jezici', [
                _buildDropdown('Njemački jezik', _nemacki, _jezici, (value) => setState(() => _nemacki = value!)),
                _buildDropdown('Engleski jezik', _engleski, _jezici, (value) => setState(() => _engleski = value!)),
              ]),
              _buildSection('Vozačka dozvola', [
                _buildMultiSelect(),
              ]),
              _buildSection('Obrazovanje', [
                _buildEducationList(),
                _buildAddButton('Dodaj obrazovanje', _addEducation),
              ]),
              _buildSection('Radno iskustvo', [
                _buildExperienceList(),
                _buildAddButton('Dodaj iskustvo', _addExperience),
              ]),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        SizedBox(height: 10),
        ...children,
        SizedBox(height: 20),
      ],
    );
  }

  Widget _buildTextField(String label, Function(String) onChanged, {TextInputType? keyboardType}) {
    return Padding(
      padding: EdgeInsets.only(bottom: 10),
      child: TextFormField(
        decoration: InputDecoration(
          labelText: label,
          border: OutlineInputBorder(),
        ),
        keyboardType: keyboardType,
        onChanged: onChanged,
        validator: (value) {
          if (value?.isEmpty ?? true) {
            return 'Ovo polje je obavezno';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildDropdown(String label, String value, List<String> items, Function(String?) onChanged) {
    return Padding(
      padding: EdgeInsets.only(bottom: 10),
      child: DropdownButtonFormField<String>(
        decoration: InputDecoration(
          labelText: label,
          border: OutlineInputBorder(),
        ),
        value: value,
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(item),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildMultiSelect() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Kategorije:'),
        SizedBox(height: 5),
        Wrap(
          spacing: 8,
          children: _vozackeKategorije.map((kategorija) {
            return FilterChip(
              label: Text(kategorija),
              selected: _vozacke.contains(kategorija),
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _vozacke.add(kategorija);
                  } else {
                    _vozacke.remove(kategorija);
                  }
                });
              },
            );
          }).toList(),
        ),
        SizedBox(height: 10),
      ],
    );
  }

  Widget _buildEducationList() {
    return Column(
      children: _obrazovanje.map((edu) {
        int index = _obrazovanje.indexOf(edu);
        return Card(
          child: ListTile(
            title: Text(edu['zvanje'] ?? ''),
            subtitle: Text('${edu['od']} - ${edu['do']}'),
            trailing: IconButton(
              icon: Icon(Icons.delete, color: Colors.red),
              onPressed: () => _removeEducation(index),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildExperienceList() {
    return Column(
      children: _iskustvo.map((exp) {
        int index = _iskustvo.indexOf(exp);
        return Card(
          child: ListTile(
            title: Text(exp['pozicija'] ?? ''),
            subtitle: Text('${exp['od']} - ${exp['do']}\n${exp['mjesto']}, ${exp['drzava']}'),
            trailing: IconButton(
              icon: Icon(Icons.delete, color: Colors.red),
              onPressed: () => _removeExperience(index),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAddButton(String text, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(Icons.add),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
    );
  }

  void _addEducation() {
    showDialog(
      context: context,
      builder: (context) => _EducationDialog(
        onSave: (education) {
          setState(() {
            _obrazovanje.add(education);
          });
        },
      ),
    );
  }

  void _addExperience() {
    showDialog(
      context: context,
      builder: (context) => _ExperienceDialog(
        onSave: (experience) {
          setState(() {
            _iskustvo.add(experience);
          });
        },
      ),
    );
  }

  void _removeEducation(int index) {
    setState(() {
      _obrazovanje.removeAt(index);
    });
  }

  void _removeExperience(int index) {
    setState(() {
      _iskustvo.removeAt(index);
    });
  }

  void _saveProfile() {
    if (_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Profil sačuvan!')),
      );
    }
  }
}

class _EducationDialog extends StatefulWidget {
  final Function(Map<String, String>) onSave;

  _EducationDialog({required this.onSave});

  @override
  _EducationDialogState createState() => _EducationDialogState();
}

class _EducationDialogState extends State<_EducationDialog> {
  final _formKey = GlobalKey<FormState>();
  String _od = '';
  String _do = '';
  String _zvanje = '';

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Dodaj obrazovanje'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              decoration: InputDecoration(labelText: 'Od (MM/YYYY)'),
              onChanged: (value) => _od = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'Do (MM/YYYY)'),
              onChanged: (value) => _do = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'Stečeno zvanje'),
              onChanged: (value) => _zvanje = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Odustani'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              widget.onSave({
                'od': _od,
                'do': _do,
                'zvanje': _zvanje,
              });
              Navigator.pop(context);
            }
          },
          child: Text('Sačuvaj'),
        ),
      ],
    );
  }
}

class _ExperienceDialog extends StatefulWidget {
  final Function(Map<String, String>) onSave;

  _ExperienceDialog({required this.onSave});

  @override
  _ExperienceDialogState createState() => _ExperienceDialogState();
}

class _ExperienceDialogState extends State<_ExperienceDialog> {
  final _formKey = GlobalKey<FormState>();
  String _od = '';
  String _do = '';
  String _pozicija = '';
  String _mjesto = '';
  String _drzava = 'Hrvatska';

  final List<String> _drzave = ['Hrvatska', 'Njemačka', 'Austrija', 'Slovenija', 'Bosna i Hercegovina', 'Srbija'];

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Dodaj radno iskustvo'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              decoration: InputDecoration(labelText: 'Od (MM/YYYY)'),
              onChanged: (value) => _od = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'Do (MM/YYYY)'),
              onChanged: (value) => _do = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'Pozicija'),
              onChanged: (value) => _pozicija = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
            TextFormField(
              decoration: InputDecoration(labelText: 'Mjesto'),
              onChanged: (value) => _mjesto = value,
              validator: (value) => value?.isEmpty ?? true ? 'Obavezno polje' : null,
            ),
            DropdownButtonFormField<String>(
              decoration: InputDecoration(labelText: 'Država'),
              value: _drzava,
              items: _drzave.map((String drzava) {
                return DropdownMenuItem<String>(
                  value: drzava,
                  child: Text(drzava),
                );
              }).toList(),
              onChanged: (value) => setState(() => _drzava = value!),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('Odustani'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              widget.onSave({
                'od': _od,
                'do': _do,
                'pozicija': _pozicija,
                'mjesto': _mjesto,
                'drzava': _drzava,
              });
              Navigator.pop(context);
            }
          },
          child: Text('Sačuvaj'),
        ),
      ],
    );
  }
}